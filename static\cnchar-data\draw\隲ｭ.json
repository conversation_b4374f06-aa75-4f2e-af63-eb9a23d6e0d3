{"strokes": ["M 289 788 Q 311 767 337 742 Q 352 729 368 730 Q 380 730 385 744 Q 389 760 380 791 Q 370 818 283 839 Q 267 842 260 839 Q 254 835 256 821 Q 260 811 289 788 Z", "M 57 606 Q 44 605 41 597 Q 38 587 53 575 Q 92 548 133 562 Q 253 605 385 623 Q 401 626 402 633 Q 403 646 385 654 Q 358 666 336 660 Q 134 615 57 606 Z", "M 176 495 Q 143 485 169 473 Q 211 460 277 480 Q 301 487 328 495 Q 344 499 346 502 Q 353 511 348 518 Q 341 528 313 531 Q 301 531 226 507 Q 202 500 176 495 Z", "M 179 395 Q 151 386 173 376 Q 203 364 270 383 Q 294 390 320 398 Q 336 402 338 405 Q 344 412 340 419 Q 333 428 309 431 Q 288 432 228 407 Q 204 400 179 395 Z", "M 158 274 Q 148 280 118 284 Q 108 287 104 282 Q 100 276 107 262 Q 132 213 148 134 Q 151 110 165 94 Q 181 75 186 89 Q 190 104 187 130 L 182 160 Q 170 227 168 249 C 166 270 166 270 158 274 Z", "M 304 179 Q 319 243 337 261 Q 356 283 336 296 Q 279 324 271 322 Q 265 322 260 317 Q 233 298 158 274 C 129 265 139 240 168 249 Q 216 264 244 272 Q 265 278 270 270 Q 276 266 270 241 Q 266 216 259 184 C 253 155 297 150 304 179 Z", "M 187 130 Q 194 129 205 132 Q 239 141 315 151 Q 325 152 325 161 Q 325 168 304 179 L 259 184 Q 255 184 252 182 Q 213 169 182 160 C 153 152 157 131 187 130 Z", "M 609 752 Q 619 771 632 783 Q 641 793 636 807 Q 632 819 602 839 Q 575 855 558 854 Q 539 851 549 831 Q 562 804 554 780 Q 527 699 483 624 Q 440 551 381 470 Q 371 460 370 454 Q 367 442 381 446 Q 444 465 585 707 Q 594 725 598 731 L 609 752 Z", "M 598 731 Q 677 629 766 536 Q 788 514 825 511 Q 892 505 964 516 Q 983 517 984 523 Q 987 529 972 535 Q 927 554 874 572 Q 765 614 620 743 Q 613 749 609 752 C 586 771 580 755 598 731 Z", "M 510 551 Q 476 544 502 529 Q 535 514 601 529 Q 632 538 668 546 Q 686 552 689 554 Q 696 561 692 570 Q 685 580 657 584 Q 633 587 565 562 Q 538 555 510 551 Z", "M 424 227 Q 425 263 429 290 L 431 317 Q 435 368 439 385 Q 439 388 439 390 C 441 404 441 404 428 412 L 426 414 Q 404 426 391 426 Q 381 423 384 414 Q 385 410 388 404 Q 387 404 389 400 Q 422 300 376 145 Q 346 85 372 51 Q 376 41 385 45 Q 421 70 421 189 Q 421 193 422 197 L 424 227 Z", "M 439 390 Q 438 391 440 391 Q 468 398 498 408 Q 508 412 516 406 Q 522 400 522 379 Q 526 211 519 111 Q 518 92 512 84 Q 509 83 463 90 Q 448 96 449 89 Q 485 52 503 26 Q 513 11 524 7 Q 531 4 537 11 Q 570 45 569 87 Q 551 358 572 396 Q 581 409 572 418 Q 556 431 529 442 Q 516 448 505 443 Q 481 428 465 422 Q 452 418 428 412 C 399 405 410 381 439 390 Z", "M 429 290 Q 468 296 495 300 Q 513 304 505 314 Q 496 324 476 327 Q 458 327 431 317 C 403 307 399 285 429 290 Z", "M 422 197 Q 462 204 494 207 Q 513 211 505 222 Q 495 234 475 236 Q 456 237 424 227 C 395 218 392 192 422 197 Z", "M 611 383 Q 630 343 618 223 Q 615 198 634 174 Q 641 167 649 175 Q 661 199 662 274 Q 662 328 666 361 Q 672 377 656 387 Q 644 394 631 401 Q 621 404 615 400 Q 608 396 611 383 Z", "M 736 408 Q 739 387 741 67 Q 741 54 734 48 Q 733 48 667 52 Q 636 62 639 53 Q 640 46 659 33 Q 717 -10 732 -35 Q 750 -63 764 -65 Q 777 -65 789 -33 Q 805 9 801 76 Q 773 323 800 434 Q 815 462 764 481 Q 733 497 715 491 Q 699 485 714 464 Q 733 439 736 408 Z"], "medians": [[[266, 831], [349, 778], [368, 748]], [[52, 593], [101, 584], [308, 633], [353, 640], [392, 636]], [[170, 485], [216, 485], [307, 510], [338, 511]], [[174, 386], [211, 386], [306, 413], [330, 413]], [[112, 277], [142, 246], [176, 94]], [[170, 272], [177, 266], [267, 296], [295, 285], [304, 274], [290, 214], [267, 190]], [[191, 138], [201, 148], [250, 162], [295, 165], [316, 160]], [[558, 841], [594, 798], [557, 708], [478, 574], [424, 499], [379, 454]], [[612, 744], [613, 732], [665, 678], [797, 563], [863, 541], [978, 525]], [[503, 541], [543, 538], [633, 561], [681, 564]], [[395, 416], [416, 385], [417, 298], [405, 178], [382, 55]], [[437, 409], [446, 404], [514, 427], [528, 422], [546, 404], [541, 331], [545, 125], [539, 72], [526, 53], [456, 85]], [[436, 296], [445, 308], [481, 313], [498, 308]], [[429, 204], [449, 218], [478, 221], [497, 216]], [[622, 390], [639, 372], [643, 358], [641, 183]], [[721, 477], [746, 462], [765, 437], [763, 230], [771, 49], [755, 9], [645, 51]]], "radStrokes": [0, 1, 2, 3, 4, 5, 6]}