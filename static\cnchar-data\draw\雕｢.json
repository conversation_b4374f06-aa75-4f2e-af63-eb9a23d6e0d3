{"strokes": ["M 191 692 Q 181 699 145 704 Q 135 707 131 702 Q 125 696 133 681 Q 158 630 175 545 Q 178 520 193 503 Q 209 482 214 497 Q 217 504 217 514 L 216 544 Q 215 548 215 552 Q 202 634 198 663 C 194 690 194 690 191 692 Z", "M 349 570 Q 368 648 392 671 Q 414 696 391 710 Q 328 743 325 743 Q 316 743 308 737 Q 266 710 191 692 C 162 685 169 655 198 663 Q 204 664 212 667 Q 252 679 287 687 Q 311 693 317 684 Q 324 677 298 568 C 291 539 342 541 349 570 Z", "M 273 525 Q 309 535 363 541 Q 372 542 373 550 Q 373 557 349 570 C 334 578 327 576 298 568 Q 252 555 216 544 C 187 535 187 510 217 514 Q 226 515 236 517 L 273 525 Z", "M 293 229 Q 294 314 298 384 L 299 417 Q 299 450 303 478 Q 309 494 300 503 Q 287 516 273 525 C 248 542 223 544 236 517 Q 251 487 251 461 Q 252 436 253 215 C 253 185 293 199 293 229 Z", "M 298 384 Q 349 397 390 404 Q 411 410 402 421 Q 390 434 366 435 Q 339 436 299 417 C 272 404 269 377 298 384 Z", "M 196 197 Q 180 326 184 357 Q 185 378 171 388 Q 149 403 124 409 Q 111 413 103 406 Q 96 400 103 387 Q 130 348 160 185 C 165 156 200 167 196 197 Z", "M 160 185 Q 97 170 80 169 Q 67 168 65 156 Q 64 143 74 134 Q 98 118 133 101 Q 143 98 156 107 Q 180 128 343 214 Q 365 224 381 239 Q 394 246 394 256 Q 388 262 375 259 Q 335 246 293 229 L 253 215 Q 223 208 196 197 L 160 185 Z", "M 553 519 Q 553 520 554 520 Q 557 529 559 539 L 561 562 Q 561 577 552 645 L 550 668 Q 543 720 542 745 C 540 775 540 775 539 776 Q 527 785 495 793 Q 485 797 481 793 Q 475 787 481 773 Q 508 700 516 586 Q 516 553 532 529 C 547 504 547 504 553 519 Z", "M 668 550 Q 687 522 705 514 Q 715 507 728 527 Q 743 555 764 705 Q 771 733 790 756 Q 802 768 791 780 Q 776 795 737 816 Q 721 822 662 807 Q 626 800 581 787 Q 557 781 539 776 C 510 768 514 734 542 745 Q 542 746 544 747 Q 592 762 677 777 Q 696 781 702 774 Q 717 758 715 742 Q 699 603 685 575 C 675 547 663 557 668 550 Z", "M 552 645 Q 555 645 560 645 Q 618 655 661 662 Q 682 666 673 677 Q 663 690 641 693 Q 599 696 550 668 C 524 653 522 645 552 645 Z", "M 559 539 Q 562 538 569 539 Q 624 546 668 550 C 698 553 712 561 685 575 Q 678 579 667 582 Q 643 588 561 562 C 532 553 529 542 559 539 Z", "M 529 412 Q 539 436 577 483 Q 586 493 574 505 Q 564 512 553 519 L 532 529 Q 528 530 522 529 Q 513 528 514 516 Q 523 450 439 353 Q 436 353 405 318 Q 398 306 410 309 Q 450 313 516 394 L 529 412 Z", "M 708 401 Q 750 408 791 412 Q 806 412 809 405 Q 815 389 809 350 Q 787 205 754 131 Q 736 83 719 70 Q 706 58 681 68 Q 653 78 628 87 Q 606 97 620 73 Q 657 22 676 -16 Q 683 -37 703 -34 Q 722 -33 746 -10 Q 815 50 862 303 Q 872 358 896 390 Q 914 409 906 420 Q 893 439 860 456 Q 836 471 810 461 Q 663 409 529 412 C 499 412 493 412 516 394 Q 528 382 541 375 C 550 369 550 369 578 375 Q 620 385 662 393 L 708 401 Z", "M 541 375 Q 541 294 419 172 Q 413 168 413 163 Q 412 159 417 159 Q 468 159 567 287 Q 571 296 597 327 Q 603 336 597 347 Q 588 365 578 375 C 558 398 542 405 541 375 Z", "M 662 393 Q 675 318 574 180 Q 570 176 565 169 Q 534 127 452 47 Q 445 41 455 40 Q 518 55 615 168 Q 673 252 723 339 Q 730 352 736 358 Q 743 362 740 372 Q 739 381 726 391 Q 719 398 708 401 C 681 413 659 423 662 393 Z"], "medians": [[[139, 695], [170, 664], [204, 502]], [[203, 672], [209, 682], [315, 713], [345, 699], [355, 686], [329, 593], [304, 580]], [[221, 522], [232, 532], [295, 550], [343, 555], [364, 550]], [[244, 512], [268, 500], [277, 474], [273, 244], [256, 228]], [[306, 389], [317, 406], [340, 414], [393, 415]], [[113, 397], [152, 357], [175, 211], [163, 197]], [[80, 154], [138, 141], [388, 253]], [[488, 786], [514, 760], [529, 675], [540, 555], [549, 524]], [[548, 755], [562, 768], [696, 797], [723, 791], [750, 765], [714, 572], [703, 551], [681, 550]], [[556, 651], [580, 665], [621, 675], [665, 671]], [[564, 544], [597, 559], [678, 571]], [[527, 517], [544, 489], [510, 419], [449, 342], [413, 317]], [[526, 393], [624, 400], [810, 435], [834, 430], [854, 408], [808, 191], [780, 109], [751, 52], [710, 19], [621, 82]], [[547, 369], [566, 348], [567, 338], [543, 291], [481, 214], [443, 178], [419, 165]], [[671, 389], [697, 362], [675, 305], [638, 237], [594, 172], [554, 126], [507, 81], [459, 46]]], "radStrokes": [0, 1, 2, 3, 4, 5, 6]}