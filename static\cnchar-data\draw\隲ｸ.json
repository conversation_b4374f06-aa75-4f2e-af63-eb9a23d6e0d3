{"strokes": ["M 328 748 Q 356 726 385 697 Q 401 684 419 684 Q 431 685 436 700 Q 442 718 430 751 Q 418 782 321 803 Q 303 807 296 803 Q 290 799 292 784 Q 296 771 328 748 Z", "M 93 564 Q 80 563 76 554 Q 73 544 89 532 Q 128 505 173 518 Q 287 560 428 582 Q 446 585 446 592 Q 447 605 428 614 Q 400 626 377 620 Q 170 572 93 564 Z", "M 225 467 Q 192 458 218 445 Q 248 430 317 447 Q 348 456 385 466 Q 403 472 405 474 Q 412 481 408 490 Q 401 500 373 504 Q 360 505 277 478 Q 253 471 225 467 Z", "M 220 363 Q 189 354 213 341 Q 255 328 312 345 Q 342 354 375 362 Q 391 368 394 370 Q 401 379 396 386 Q 389 396 361 399 Q 336 402 272 375 Q 248 368 220 363 Z", "M 220 260 Q 210 267 172 272 Q 160 275 157 269 Q 151 263 160 247 Q 185 199 205 119 Q 209 94 223 79 Q 242 57 247 74 Q 251 87 249 110 L 243 143 Q 231 209 228 232 C 224 258 224 258 220 260 Z", "M 376 159 Q 397 222 416 239 Q 438 264 415 278 Q 352 311 349 311 Q 340 311 332 305 Q 299 284 220 260 C 191 251 201 220 228 232 Q 244 241 311 255 Q 335 261 341 252 Q 347 246 328 169 C 321 140 366 131 376 159 Z", "M 249 110 Q 267 119 379 135 Q 389 136 389 145 Q 389 151 376 159 C 354 174 354 174 328 169 Q 321 168 316 166 Q 276 153 243 143 C 214 134 221 99 249 110 Z", "M 637 571 Q 724 595 728 598 Q 735 605 731 613 Q 724 623 697 629 Q 669 633 643 620 Q 640 619 640 618 C 616 604 616 604 590 601 Q 584 601 579 599 Q 542 589 498 582 Q 465 575 490 561 Q 526 543 589 558 L 637 571 Z", "M 629 472 Q 633 523 637 571 L 640 618 Q 644 729 663 798 Q 666 808 646 824 Q 610 843 586 847 Q 570 851 562 842 Q 555 835 563 819 Q 584 789 585 768 Q 589 689 590 601 L 589 558 Q 589 513 587 464 C 586 434 627 442 629 472 Z", "M 724 452 Q 733 453 973 472 Q 992 473 997 482 Q 1001 494 985 506 Q 927 545 864 522 Q 815 510 751 495 L 690 483 Q 684 484 629 472 L 587 464 Q 505 451 415 434 Q 396 431 411 415 Q 424 402 442 398 Q 463 394 478 399 Q 562 427 658 442 Q 659 443 665 443 L 724 452 Z", "M 629 320 Q 681 390 724 452 L 751 495 Q 770 525 786 553 Q 825 623 849 656 Q 858 669 849 685 Q 831 716 813 730 Q 797 743 779 744 Q 761 744 767 721 Q 773 682 770 661 Q 728 547 690 483 L 665 443 Q 625 385 568 312 L 531 268 Q 437 162 323 59 Q 316 53 315 45 Q 315 39 322 40 Q 332 41 341 47 Q 443 114 492 165 Q 513 186 535 208 L 629 320 Z", "M 557 312 Q 550 318 540 321 Q 531 325 521 321 Q 515 317 520 306 Q 521 300 524 295 Q 530 286 531 268 L 535 208 Q 538 148 530 107 Q 518 62 551 20 Q 561 10 570 20 Q 580 35 580 54 L 581 86 Q 581 131 581 175 L 581 203 Q 581 246 581 260 C 581 290 581 296 557 312 Z", "M 714 63 Q 739 29 758 -4 Q 765 -20 777 -20 Q 792 -19 808 11 Q 820 41 821 114 Q 803 253 829 284 Q 838 294 830 305 Q 814 323 768 347 Q 752 356 738 348 Q 699 332 640 322 Q 633 321 629 320 L 568 312 Q 562 312 557 312 C 527 310 562 237 581 260 Q 602 290 633 293 Q 678 299 720 305 Q 739 308 749 300 Q 764 285 758 146 Q 758 107 755 96 Q 751 80 740 84 C 714 68 712 66 714 63 Z", "M 581 175 Q 656 184 710 189 Q 732 193 725 205 Q 715 218 691 224 Q 658 228 581 203 C 552 194 551 171 581 175 Z", "M 580 54 Q 583 54 587 54 Q 629 61 714 63 C 744 64 761 64 740 84 Q 733 91 720 100 Q 705 110 677 103 Q 625 93 581 86 C 551 81 550 54 580 54 Z"], "medians": [[[303, 794], [393, 740], [416, 704]], [[87, 550], [149, 542], [371, 596], [404, 600], [436, 595]], [[219, 457], [258, 455], [367, 483], [397, 484]], [[214, 353], [258, 353], [356, 379], [386, 379]], [[166, 262], [196, 238], [235, 79]], [[229, 240], [238, 251], [285, 267], [340, 281], [355, 277], [379, 254], [359, 189], [336, 180]], [[250, 116], [268, 134], [314, 146], [380, 144]], [[491, 573], [546, 570], [678, 607], [722, 607]], [[574, 833], [612, 803], [621, 789], [610, 495], [593, 472]], [[414, 424], [468, 420], [644, 459], [893, 498], [940, 498], [986, 487]], [[779, 730], [798, 705], [811, 672], [767, 575], [709, 469], [617, 339], [515, 217], [434, 138], [322, 47]], [[528, 313], [544, 297], [557, 256], [554, 82], [561, 27]], [[563, 307], [587, 294], [608, 303], [740, 327], [765, 321], [787, 297], [789, 105], [774, 51], [777, -4]], [[588, 182], [597, 194], [634, 200], [685, 206], [714, 200]], [[584, 59], [602, 73], [673, 83], [733, 84]]], "radStrokes": [0, 1, 2, 3, 4, 5, 6]}