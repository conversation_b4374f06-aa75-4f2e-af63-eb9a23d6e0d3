export default ({ $axios }, inject) => {
  inject('menuList', (objParam) => $axios.$get(`/sanhong/home/<USER>
  inject('getHomecontents', (objParam) => $axios.$get(`/sanhong/home/<USER>
  inject('getcontents', (refreshKey, category, page) => $axios.$get(`/sanhong/getContent?refreshKey=${refreshKey}&category=${category}&page=${page}`));
  inject('relatedReadings', (objParam) => $axios.$post(`/sanhong/relatedReadings`, objParam));
  inject('getcontenttitle', (objParam) => $axios.$get(`/sanhong/getContentTitle`, objParam));
  inject('searchContent', (objParam) => $axios.$get(`/sanhong/searchContent?keyword=${objParam}`));
  inject('getSearchContent', (refreshKey, keyword, page) => $axios.$get(`/sanhong/getSearchContent?refreshKey=${refreshKey}&keyword=${keyword}&page=${page}`));
  inject('getcontentMessage', (contentid) => $axios.$get(`/sanhong/view?contentid=${contentid}`));
  inject('setOrder', (objParam) => $axios.$post(`/sanhong/setOrder`, objParam));
  inject('getOrder', (ordernumber) => $axios.$get(`/sanhong/getOrder?transaction_id=${ordernumber}`));
  inject('getSuccessOrder', (transaction_id) => $axios.$get(`/sanhong/getSuccessOrder?transaction_id=${transaction_id}`));
  inject('layoutRelatedReadings', (objParam) => $axios.$post(`/sanhong/layoutRelatedReadings`, objParam));
  inject('setOrderFromData', (objParam) => $axios.$post(`/sanhong/setOrderFromData`, objParam));
  inject('getOrderFromData', (ordernumber) => $axios.$get(`/sanhong/getOrderFromData?ordernumber=${ordernumber}`));
  // inject('gettraffic', (cip,cname) =>
  //   $axios.$get(`/sanhong/traffic?cip=${cip}&cname=${cname}`)
  // )
};
