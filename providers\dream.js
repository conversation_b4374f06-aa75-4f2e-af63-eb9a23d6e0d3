export default ({ $axios }, inject) => {
  inject('getdreamCategories', (objParam) => $axios.$get(`/sanhong/dream/dreamCategories`, objParam));
  inject('getdreamCategorieDetails', (dreamname, page) => $axios.$get(`/sanhong/dream/dreamCategorieDetails?dreamname=${dreamname}&page=${page}`));
  inject('gethotDream', (objParam) => $axios.$get(`/sanhong/dream/hotDream`, objParam));
  inject('getdreamContents', (dreamname) => $axios.$get(`/sanhong/dream/dreamContents?dreamname=${dreamname}`));
  inject('getdreamDetails', (objParam) => $axios.$post(`/sanhong/dream/details`, objParam));
  inject('getsearchDetails', (objParam) => $axios.$post(`/sanhong/dream/searchDetails`, objParam));
  inject('createOrder', (objParam) => $axios.$post(`/sanhong/dream/createOrder`, objParam));
  inject('dreamGetOrder', (objParam) => $axios.$post(`/sanhong/dream/getOrder`, objParam));
  inject('dreamUpdateOrder', (objParam) => $axios.$post(`/sanhong/dream/updateOrder`, objParam));
  inject('createWXpay', (objParam) => $axios.$post(`/sanhong/wxpay`, objParam));
  inject('orderWXpay', (objParam) => $axios.$post(`/sanhong/wxpay/order`, objParam));
  inject('getheartDream', (objParam) => $axios.$get(`/sanhong/dream/heartDream`, objParam));
  inject('createWXpaymweb', (objParam) => $axios.$post(`/sanhong/wxpaymweb`, objParam));
  inject('orderWXpaymweb', (objParam) => $axios.$post(`/sanhong/wxpaymweb/order`, objParam));
  inject('getDreamKeyword', (label) => $axios.$get(`/sanhong/dream/keyword?label=${label}`));
  inject('getshchat', (objParam) => $axios.$post(`/sanhong/api/shchat`, objParam));
};
